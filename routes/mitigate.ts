import { Router } from "express";
import ollama from "ollama";
import { z } from "zod";
import vulnerabilitiesData from "../assets/vulnerabilities.json";

const mitigateRouter = Router();

// Simple input schema - just need a user query
const inputSchema = z.object({
  user_query: z.string().min(1, "User query is required"),
});

// Helper function to create system prompt
const createSystemPrompt = (vulnerabilities: any[]) => {
  return `You are a cybersecurity expert assistant. You have access to a comprehensive vulnerability database and can help users understand security threats, analyze vulnerabilities, and provide guidance on cybersecurity matters.

Available Vulnerabilities Database:
${JSON.stringify(vulnerabilities, null, 2)}

Please provide helpful, accurate information about cybersecurity topics based on the vulnerability data and your expertise. Be conversational and informative.`;
};

mitigateRouter.post("/ai", async (req, res) => {
  try {
    console.log("🚀 Starting AI chat request");
    console.log("📝 Request body:", JSON.stringify(req.body, null, 2));

    // Validate input
    const validationResult = inputSchema.safeParse(req.body);
    if (!validationResult.success) {
      console.error(
        "❌ Input validation failed:",
        validationResult.error.errors
      );
      res.status(400).json({
        error: "Invalid input",
        details: validationResult.error.errors,
      });
      return;
    }

    const { user_query } = validationResult.data;
    console.log("💬 User query:", user_query);

    // Use imported vulnerabilities data
    const vulnerabilities = vulnerabilitiesData;
    console.log(
      `📊 Loaded ${vulnerabilities.length} vulnerabilities from imported data`
    );

    // Create system prompt
    const systemPrompt = createSystemPrompt(vulnerabilities);
    console.log("🧠 System prompt created");

    // Set up streaming response
    res.writeHead(200, {
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    });

    console.log("🌊 Starting streaming response");

    // Start streaming chat
    const stream = await ollama.chat({
      model: "deepseek-r1",
      messages: [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: user_query,
        },
      ],
      stream: true,
    });

    console.log("🔄 Ollama stream started");

    // Collect response text for concatenated logging
    let fullResponse = "";

    // Stream the response
    for await (const chunk of stream) {
      if (chunk.message?.content) {
        fullResponse += chunk.message.content;
        console.log("📄 Full concatenated response:", fullResponse);
        res.write(chunk.message.content);
      }
    }

    console.log("✅ Stream completed");
    console.log("📄 Full concatenated response:", fullResponse);
    res.end();
  } catch (error) {
    console.error("💥 Error in /ai endpoint:", error);

    // If headers haven't been sent yet, send error response
    if (!res.headersSent) {
      res.status(500).json({
        error: "Internal server error",
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    } else {
      // If streaming has started, just end the stream
      res.end();
    }
  }
});

export default mitigateRouter;
